<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Currency Input Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-input {
            width: 300px;
            padding: 12px;
            font-size: 16px;
            border: 2px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .test-input:focus {
            border-color: #007cba;
            outline: none;
        }
        
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .results {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Currency Input Test Page</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li><strong>Cursor Position Test:</strong> Click in the middle of a formatted number and type. The cursor should stay where you clicked.</li>
            <li><strong>R$ Symbol Test:</strong> The "R$ " symbol should appear at the left of each input field.</li>
            <li><strong>Natural Editing:</strong> You should be able to edit numbers naturally without the cursor jumping to the end.</li>
            <li><strong>Formatting Test:</strong> Numbers should format as you type (e.g., 1234 becomes 1.234,00).</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>Test 1: Basic Currency Input</h3>
        <p>Type some numbers and test cursor positioning:</p>
        <input type="text" class="test-input" data-currency="true" placeholder="Enter amount">
        <div class="results" id="result1">Value: <span id="value1">-</span></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Pre-filled Currency Input</h3>
        <p>This input has a pre-filled value. Try editing in the middle:</p>
        <input type="text" class="test-input" data-currency="true" value="123456" placeholder="Enter amount">
        <div class="results" id="result2">Value: <span id="value2">-</span></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Multiple Currency Inputs</h3>
        <p>Test multiple inputs to ensure no conflicts:</p>
        <input type="text" class="test-input" data-currency="true" placeholder="Amount 1">
        <br>
        <input type="text" class="test-input" data-currency="true" placeholder="Amount 2">
        <br>
        <input type="text" class="test-input" data-currency="true" placeholder="Amount 3">
    </div>

    <div class="test-section">
        <h3>Test 4: Focus and Selection Test</h3>
        <p>Tab between these inputs to test focus behavior:</p>
        <input type="text" class="test-input" data-currency="true" placeholder="Tab to me first">
        <input type="text" class="test-input" data-currency="true" placeholder="Then tab to me">
        <input type="text" class="test-input" data-currency="true" placeholder="Finally tab here">
    </div>

    <!-- Load the currency formatter -->
    <script src="dist/currency/index.js"></script>
    
    <script>
        // Add some debugging to see what's happening
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input[data-currency="true"]');
            
            inputs.forEach((input, index) => {
                input.addEventListener('input', function() {
                    console.log(`Input ${index + 1} changed:`, {
                        value: this.value,
                        cursorPosition: this.selectionStart,
                        hasClass: this.classList.contains('currency-input')
                    });
                });
                
                input.addEventListener('focus', function() {
                    console.log(`Input ${index + 1} focused:`, {
                        value: this.value,
                        cursorPosition: this.selectionStart,
                        selectionEnd: this.selectionEnd
                    });
                });
                
                input.addEventListener('click', function() {
                    console.log(`Input ${index + 1} clicked:`, {
                        cursorPosition: this.selectionStart
                    });
                });
            });
            
            // Check if currency inputs are properly initialized
            setTimeout(() => {
                console.log('Currency inputs status:');
                inputs.forEach((input, index) => {
                    console.log(`Input ${index + 1}:`, {
                        hasClass: input.classList.contains('currency-input'),
                        hasDataAttr: input.dataset.currencyFormatter,
                        paddingLeft: getComputedStyle(input).paddingLeft
                    });
                });
            }, 1000);
        });
    </script>
</body>
</html>
