/**
 * <PERSON><PERSON>rency Formatter Styles for Webflow - jQuery Approach
 *
 * Clean CSS for currency inputs where R$ symbol is included in the value
 * No pseudo-elements needed - symbol is part of the formatted text
 */

/* Base currency input styling - jQuery approach */
.currency-input {
  text-align: right !important; /* Right-aligned like jQuery version */
  font-weight: 500 !important;
  font-feature-settings: 'tnum' 1; /* Use tabular numbers for better alignment */
  font-variant-numeric: tabular-nums;
}

/* Webflow form styling compatibility */
.w-input.currency-input {
  text-align: right !important;
  font-weight: 500 !important;
}

/* Responsive design for mobile devices */
@media (max-width: 479px) {
  .currency-input {
    font-size: 14px;
  }
}

/* Better support for different Webflow form styles */
.w-form .currency-input {
  text-align: right !important;
  font-weight: 500 !important;
}

/* Accessibility improvements */
.currency-input:focus {
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .currency-input {
    color: #000 !important;
  }
}
