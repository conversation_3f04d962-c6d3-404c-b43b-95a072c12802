// Script específico para a calculadora Reino Capital baseado na estrutura HTML do Playwright
// Estrutura identificada: spinbutton [ref=e45] dentro do form "Email Form" [ref=e25]

(function () {
  "use strict";

  console.log("🔍 Iniciando script para Reino Capital Calculadora...");

  function initReinoCurrencyFormatter() {
    // Baseado no Playwright: spinbutton [ref=e45] dentro do form "Email Form" [ref=e25]
    let targetInput = null;

    // Método 1: Procurar por spinbutton (baseado no snapshot do Playwright)
    const spinbutton = document.querySelector('[role="spinbutton"]');
    if (spinbutton) {
      console.log("✅ Spinbutton encontrado:", spinbutton);
      targetInput = spinbutton;

      // Adicionar ID para referência
      if (!targetInput.id) {
        targetInput.id = "currency";
        console.log('✅ ID "currency" adicionado ao spinbutton');
      }
    }

    // Método 2: Procurar dentro do form "Email Form"
    if (!targetInput) {
      const emailForm =
        document.querySelector('form[aria-label="Email Form"]') ||
        document.querySelector('form[name="email-form"]') ||
        document.querySelector("form");

      if (emailForm) {
        console.log("📝 Form encontrado:", emailForm);

        // Procurar input numérico dentro do form
        const numericInput =
          emailForm.querySelector('input[type="number"]') ||
          emailForm.querySelector('input[inputmode="numeric"]') ||
          emailForm.querySelector("input");

        if (numericInput) {
          console.log("✅ Input numérico encontrado no form:", numericInput);
          targetInput = numericInput;
          targetInput.id = "currency";
        }
      }
    }

    // Método 3: Procurar por padrões específicos da página
    if (!targetInput) {
      // Baseado no conteúdo visto: "Qual o seu patrimônio atual?"
      const patrimonioSection = Array.from(document.querySelectorAll("*")).find(
        (el) => el.textContent && el.textContent.includes("patrimônio atual")
      );

      if (patrimonioSection) {
        console.log("📍 Seção de patrimônio encontrada:", patrimonioSection);

        // Procurar input próximo
        const nearbyInput =
          patrimonioSection.closest("div").querySelector("input") ||
          patrimonioSection.parentElement.querySelector("input");

        if (nearbyInput) {
          console.log("✅ Input próximo à seção de patrimônio:", nearbyInput);
          targetInput = nearbyInput;
          targetInput.id = "currency";
        }
      }
    }

    if (!targetInput) {
      console.log("❌ Nenhum input adequado encontrado");
      return;
    }

    console.log("🎯 Input alvo selecionado:", targetInput);

    // Configurar formatação de moeda brasileira
    function formatBRL(value) {
      // Remove tudo que não é número
      const numbers = String(value).replace(/[^\d]/g, "");

      if (!numbers) return "";

      // Converte para número (centavos)
      const cents = parseInt(numbers, 10);
      const reais = cents / 100;

      // Formata para BRL
      return reais.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    }

    // Obter valor numérico
    function getNumericValue(formatted) {
      const numbers = String(formatted).replace(/[^\d]/g, "");
      return parseInt(numbers, 10) / 100;
    }

    // Aplicar formatação inicial
    if (targetInput.value) {
      const initialValue = targetInput.value;
      targetInput.value = formatBRL(initialValue);
      console.log(
        `🎨 Formatação inicial aplicada: "${initialValue}" → "${targetInput.value}"`
      );
    }

    // Event listener para formatação em tempo real
    targetInput.addEventListener("input", function (e) {
      const input = e.target;
      const cursorPos = input.selectionStart;
      const oldValue = input.value;
      const oldLength = oldValue.length;

      // Formatar valor
      const newValue = formatBRL(oldValue);
      input.value = newValue;

      // Calcular nova posição do cursor
      const newLength = newValue.length;
      const lengthDiff = newLength - oldLength;
      let newCursorPos = cursorPos + lengthDiff;

      // Ajustar cursor para não ficar no meio do símbolo da moeda
      if (newCursorPos < 3) newCursorPos = 3; // Depois de "R$ "
      if (newCursorPos > newLength) newCursorPos = newLength;

      // Aplicar nova posição do cursor
      setTimeout(() => {
        input.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    });

    // Formatação ao perder foco
    targetInput.addEventListener("blur", function (e) {
      if (e.target.value && !e.target.value.includes("R$")) {
        e.target.value = formatBRL(e.target.value);
      }
    });

    // Melhorar UX
    targetInput.setAttribute("placeholder", "R$ 0,00");
    targetInput.setAttribute("data-currency", "true");
    targetInput.style.textAlign = "right";

    // Adicionar estilos
    const style = document.createElement("style");
    style.textContent = `
      input[data-currency="true"] {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-weight: 500;
        letter-spacing: 0.5px;
      }
      
      input[data-currency="true"]:focus {
        outline: 2px solid #DAA520;
        outline-offset: 2px;
      }
    `;
    document.head.appendChild(style);

    console.log("✅ Formatação de moeda configurada com sucesso!");

    // Teste
    console.log("🧪 Testes de formatação:");
    console.log("10000 → ", formatBRL(10000));
    console.log("123456 → ", formatBRL(123456));
    console.log("50000 → ", formatBRL(50000));

    // Expor funções globalmente
    window.formatBRL = formatBRL;
    window.getNumericValue = getNumericValue;

    return true;
  }

  // Executar quando DOM estiver pronto
  function tryInit() {
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", initReinoCurrencyFormatter);
    } else {
      initReinoCurrencyFormatter();
    }
  }

  tryInit();

  // Fallback com delay (para aguardar Webflow carregar completamente)
  setTimeout(initReinoCurrencyFormatter, 1000);
  setTimeout(initReinoCurrencyFormatter, 2000);

  // Observer para mudanças no DOM
  const observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
      if (mutation.type === "childList" || mutation.type === "attributes") {
        const spinbutton = document.querySelector('[role="spinbutton"]');
        if (spinbutton && !spinbutton.hasAttribute("data-currency")) {
          console.log("🔄 Reaplicando formatação após mudança no DOM");
          initReinoCurrencyFormatter();
        }
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ["role", "class", "id"],
  });
})();

// Função manual para debug
window.debugReinoCurrency = function () {
  console.log("🔧 Debug - Elementos encontrados:");

  const spinbutton = document.querySelector('[role="spinbutton"]');
  console.log("Spinbutton:", spinbutton);

  const form = document.querySelector("form");
  console.log("Form:", form);

  const inputs = document.querySelectorAll("input");
  console.log("Todos os inputs:", inputs);

  inputs.forEach((input, i) => {
    console.log(`Input ${i + 1}:`, {
      element: input,
      type: input.type,
      role: input.getAttribute("role"),
      id: input.id,
      name: input.name,
      value: input.value,
      className: input.className,
    });
  });
};
