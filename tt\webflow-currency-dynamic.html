// SCRIPT FINAL PARA WEBFLOW - Reino Capital Calculadora com Currency.js e jQuery
// Versão com carregamento dinâmico do Currency.js usando jQuery
// Copie este código e cole no final da página antes do </body>

<script>
(function($) {
  'use strict';
  
  console.log('🚀 Reino Capital - Currency Formatter com Currency.js + jQuery v2.0');
  
  let currencyLoaded = false;
  
  // Função para carregar Currency.js dinamicamente usando jQuery
  function carregarCurrencyJS() {
    return new Promise((resolve, reject) => {
      if (typeof currency !== 'undefined') {
        console.log('✅ Currency.js já disponível');
        currencyLoaded = true;
        resolve();
        return;
      }
      
      console.log('📦 Carregando Currency.js com jQuery...');
      
      // Usar jQuery getScript para carregar Currency.js
      $.getScript('https://cdn.jsdelivr.net/npm/currency.js@2.0.4/dist/currency.min.js')
        .done(function() {
          console.log('✅ Currency.js carregado com sucesso via jQuery');
          currencyLoaded = true;
          resolve();
        })
        .fail(function() {
          console.error('❌ Erro ao carregar Currency.js, usando fallback');
          currencyLoaded = false;
          resolve(); // Resolve mesmo com erro para usar fallback
        });
    });
  }
  
  function aplicarFormatacao() {
    // Procurar input baseado na estrutura HTML específica do Playwright
    let input = document.querySelector('[role="spinbutton"]') || 
               document.querySelector('form input[type="number"]') ||
               document.querySelector('form input');
    
    if (!input) {
      console.log('❌ Input não encontrado');
      return false;
    }
    
    // Verificar se já foi configurado
    if (input.hasAttribute('data-currency-configured')) {
      return true;
    }
    
    console.log('✅ Input encontrado, aplicando formatação...', input);
    
    // Marcar como configurado
    input.setAttribute('data-currency-configured', 'true');
    input.id = 'currency';
    
    // Função de formatação com Currency.js
    function formatarMoedaCurrency(valor) {
      const currencyConfig = {
        symbol: 'R$ ',
        precision: 2,
        separator: ',',
        delimiter: '.',
        format: '%s%v'
      };
      
      const numeros = String(valor).replace(/[^\d]/g, '');
      if (!numeros) return '';
      
      const centavos = parseInt(numeros, 10);
      const reais = centavos / 100;
      
      return currency(reais, currencyConfig).format();
    }
    
    // Função de formatação nativa (fallback)
    function formatarMoedaNativa(valor) {
      const numeros = String(valor).replace(/[^\d]/g, '');
      if (!numeros) return '';
      
      const centavos = parseInt(numeros, 10);
      const reais = centavos / 100;
      
      return reais.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      });
    }
    
    // Escolher função de formatação
    const formatarMoeda = (currencyLoaded && typeof currency !== 'undefined') 
      ? formatarMoedaCurrency 
      : formatarMoedaNativa;
    
    console.log('💰 Usando formatação:', currencyLoaded ? 'Currency.js' : 'Nativa');
    
    // Aplicar formatação inicial
    if (input.value) {
      input.value = formatarMoeda(input.value);
    }
    
    // Event listener para formatação em tempo real
    input.addEventListener('input', function(e) {
      const cursor = e.target.selectionStart;
      const valorAntigo = e.target.value;
      const tamanhoAntigo = valorAntigo.length;
      
      const valorFormatado = formatarMoeda(valorAntigo);
      e.target.value = valorFormatado;
      
      // Ajustar cursor
      const novoTamanho = valorFormatado.length;
      const diferenca = novoTamanho - tamanhoAntigo;
      let novoCursor = cursor + diferenca;
      
      if (novoCursor < 3) novoCursor = 3; // Depois de "R$ "
      if (novoCursor > novoTamanho) novoCursor = novoTamanho;
      
      setTimeout(() => {
        e.target.setSelectionRange(novoCursor, novoCursor);
      }, 0);
    });
    
    // Formatação ao perder foco
    input.addEventListener('blur', function(e) {
      if (e.target.value && !e.target.value.includes('R$')) {
        e.target.value = formatarMoeda(e.target.value);
      }
    });
    
    // Melhorar aparência
    input.setAttribute('placeholder', 'R$ 0,00');
    input.style.textAlign = 'right';
    input.style.fontWeight = '500';
    
    // Teste da formatação
    console.log('🧪 Testando formatação:');
    console.log('100000 →', formatarMoeda('100000'));
    console.log('1234567 →', formatarMoeda('1234567'));
    console.log('50000 →', formatarMoeda('50000'));
    
    console.log('✅ Formatação aplicada com sucesso!');
    return true;
  }
  
  // Função principal que aguarda Currency.js e aplica formatação
  async function inicializar() {
    try {
      await carregarCurrencyJS();
      aplicarFormatacao();
    } catch (error) {
      console.error('❌ Erro na inicialização:', error);
      aplicarFormatacao(); // Tentar mesmo com erro
    }
  }
  
  // Tentar aplicar imediatamente
  inicializar();
  
  // Tentar após DOM carregar
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', inicializar);
  }
  
  // Tentativas com delay (para aguardar Webflow)
  setTimeout(inicializar, 500);
  setTimeout(inicializar, 1000);
  setTimeout(inicializar, 2000);
  
  // Observer para mudanças no DOM
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        const spinbutton = document.querySelector('[role="spinbutton"]');
        if (spinbutton && !spinbutton.hasAttribute('data-currency-configured')) {
          console.log('🔄 Novo spinbutton detectado, aplicando formatação...');
          inicializar();
        }
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Função global para debug
  window.debugReinoCurrency = function() {
    console.log('🔧 Debug Reino Currency:');
    console.log('Currency.js carregado:', currencyLoaded);
    console.log('Currency.js disponível:', typeof currency !== 'undefined');
    const input = document.querySelector('[role="spinbutton"]');
    console.log('Spinbutton:', input);
    if (input) {
      console.log('Configurado:', input.hasAttribute('data-currency-configured'));
      console.log('Valor:', input.value);
    }
    inicializar();
  };
  
  // Expor função de formatação globalmente
  window.formatarMoedaReino = function(valor) {
    if (currencyLoaded && typeof currency !== 'undefined') {
      const currencyConfig = {
        symbol: 'R$ ',
        precision: 2,
        separator: ',',
        delimiter: '.',
        format: '%s%v'
      };
      
      const numeros = String(valor).replace(/[^\d]/g, '');
      if (!numeros) return '';
      
      const centavos = parseInt(numeros, 10);
      const reais = centavos / 100;
      
      return currency(reais, currencyConfig).format();
    } else {
      const numeros = String(valor).replace(/[^\d]/g, '');
      if (!numeros) return '';
      
      const centavos = parseInt(numeros, 10);
      const reais = centavos / 100;
      
      return reais.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      });
    }
  };
  
})();
</script>
