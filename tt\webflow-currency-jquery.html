// SCRIPT FINAL PARA WEBFLOW - Reino Capital Calculadora com Currency.js e jQuery
// Versão jQuery com carregamento dinâmico do Currency.js
// Copie este código e cole no final da página antes do </body>

<script>
(function($) {
  console.log('🚀 Reino Capital - Currency Formatter com Currency.js + jQuery v2.0');
  
  let currencyLoaded = false;
  
  // Função para carregar Currency.js dinamicamente
  function carregarCurrencyJS() {
    return new Promise((resolve, reject) => {
      // Verificar se Currency.js está realmente disponível e funcional
      if (typeof currency !== 'undefined' && typeof currency === 'function') {
        console.log('✅ Currency.js já disponível e funcional');
        currencyLoaded = true;
        resolve();
        return;
      }
      
      console.log('📦 Carregando Currency.js...');
      
      // Primeiro, tentar carregar via jQuery
      $.getScript('https://cdn.jsdelivr.net/npm/currency.js@2.0.4/dist/currency.min.js')
        .done(function() {
          // Aguardar um pouco para garantir que a biblioteca esteja disponível
          setTimeout(() => {
            if (typeof currency !== 'undefined' && typeof currency === 'function') {
              console.log('✅ Currency.js carregado com sucesso via jQuery');
              currencyLoaded = true;
              resolve();
            } else {
              console.error('❌ Currency.js carregado mas não funcional, tentando método alternativo');
              carregarCurrencyAlternativo().then(resolve);
            }
          }, 100);
        })
        .fail(function() {
          console.error('❌ Erro ao carregar Currency.js via jQuery, tentando método alternativo');
          carregarCurrencyAlternativo().then(resolve);
        });
    });
  }
  
  // Método alternativo para carregar Currency.js
  function carregarCurrencyAlternativo() {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/currency.js@2.0.4/dist/currency.min.js';
      
      script.onload = function() {
        setTimeout(() => {
          if (typeof currency !== 'undefined' && typeof currency === 'function') {
            console.log('✅ Currency.js carregado com sucesso via método alternativo');
            currencyLoaded = true;
          } else {
            console.error('❌ Currency.js ainda não funcional, usando fallback');
            currencyLoaded = false;
          }
          resolve();
        }, 100);
      };
      
      script.onerror = function() {
        console.error('❌ Erro ao carregar Currency.js, usando fallback');
        currencyLoaded = false;
        resolve();
      };
      
      document.head.appendChild(script);
    });
  }
  
  function aplicarFormatacao() {
    // Procurar input baseado na estrutura HTML específica do Playwright
    let $input = $('[role="spinbutton"]').first();
    
    if (!$input.length) {
      $input = $('form input[type="number"]').first();
    }
    
    if (!$input.length) {
      $input = $('form input').first();
    }
    
    if (!$input.length) {
      console.log('❌ Input não encontrado');
      return false;
    }
    
    // Verificar se já foi configurado
    if ($input.attr('data-currency-configured')) {
      return true;
    }
    
    console.log('✅ Input encontrado, aplicando formatação...', $input[0]);
    
    // Marcar como configurado
    $input.attr('data-currency-configured', 'true');
    $input.attr('id', 'currency');
    
    // Função de formatação com Currency.js
    function formatarMoedaCurrency(valor) {
      // Validação extra para garantir que currency está disponível
      if (typeof currency !== 'function') {
        console.warn('⚠️ Currency.js não disponível, usando fallback');
        return formatarMoedaNativa(valor);
      }
      
      try {
        const currencyConfig = {
          symbol: 'R$ ',
          precision: 2,
          separator: ',',
          delimiter: '.',
          format: '%s%v'
        };
        
        const numeros = String(valor).replace(/[^\d]/g, '');
        if (!numeros) return '';
        
        const centavos = parseInt(numeros, 10);
        const reais = centavos / 100;
        
        return currency(reais, currencyConfig).format();
      } catch (error) {
        console.error('❌ Erro ao usar Currency.js:', error);
        return formatarMoedaNativa(valor);
      }
    }
    
    // Função de formatação nativa (fallback)
    function formatarMoedaNativa(valor) {
      const numeros = String(valor).replace(/[^\d]/g, '');
      if (!numeros) return '';
      
      const centavos = parseInt(numeros, 10);
      const reais = centavos / 100;
      
      return reais.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      });
    }
    
    // Escolher função de formatação com validação extra
    const formatarMoeda = (currencyLoaded && typeof currency === 'function') 
      ? formatarMoedaCurrency 
      : formatarMoedaNativa;
    
    console.log('💰 Usando formatação:', (currencyLoaded && typeof currency === 'function') ? 'Currency.js' : 'Nativa');
    console.log('🔍 Status Currency.js:', {
      currencyLoaded: currencyLoaded,
      typeofCurrency: typeof currency,
      isFunctionCurrency: typeof currency === 'function'
    });
    
    // Aplicar formatação inicial
    if ($input.val()) {
      $input.val(formatarMoeda($input.val()));
    }
    
    // Event listener para formatação em tempo real
    $input.on('input', function(e) {
      const $this = $(this);
      const inputEl = this;
      const cursor = inputEl.selectionStart;
      const valorAntigo = $this.val();
      const tamanhoAntigo = valorAntigo.length;
      
      const valorFormatado = formatarMoeda(valorAntigo);
      $this.val(valorFormatado);
      
      // Ajustar cursor
      const novoTamanho = valorFormatado.length;
      const diferenca = novoTamanho - tamanhoAntigo;
      let novoCursor = cursor + diferenca;
      
      if (novoCursor < 3) novoCursor = 3; // Depois de "R$ "
      if (novoCursor > novoTamanho) novoCursor = novoTamanho;
      
      setTimeout(() => {
        inputEl.setSelectionRange(novoCursor, novoCursor);
      }, 0);
    });
    
    // Formatação ao perder foco
    $input.on('blur', function(e) {
      const $this = $(this);
      const valor = $this.val();
      
      if (valor && !valor.includes('R$')) {
        $this.val(formatarMoeda(valor));
      }
    });
    
    // Melhorar aparência
    $input.attr('placeholder', 'R$ 0.000,000,00');
    $input.css({
      'text-align': 'right',
      'font-weight': '500'
    });
    
    // Teste da formatação com try/catch
    console.log('🧪 Testando formatação:');
    try {
      console.log('100000 →', formatarMoeda('100000'));
      console.log('1234567 →', formatarMoeda('1234567'));
      console.log('50000 →', formatarMoeda('50000'));
    } catch (error) {
      console.error('❌ Erro no teste de formatação:', error);
    }
    
    console.log('✅ Formatação aplicada com sucesso!');
    return true;
  }
  
  // Função principal que aguarda Currency.js e aplica formatação
  async function inicializar() {
    try {
      await carregarCurrencyJS();
      aplicarFormatacao();
    } catch (error) {
      console.error('❌ Erro na inicialização:', error);
      aplicarFormatacao(); // Tentar mesmo com erro
    }
  }
  
  // Tentar aplicar imediatamente
  inicializar();
  
  // Tentar após DOM carregar
  $(document).ready(function() {
    inicializar();
  });
  
  // Tentativas com delay (para aguardar Webflow)
  setTimeout(inicializar, 500);
  setTimeout(inicializar, 1000);
  setTimeout(inicializar, 2000);
  
  // Observer para mudanças no DOM usando jQuery
  let observerTimeout;
  function checkForNewSpinbutton() {
    const $spinbutton = $('[role="spinbutton"]');
    
    if ($spinbutton.length && !$spinbutton.attr('data-currency-configured')) {
      console.log('🔄 Novo spinbutton detectado, aplicando formatação...');
      inicializar();
    }
    
    // Verificar novamente após um tempo
    clearTimeout(observerTimeout);
    observerTimeout = setTimeout(checkForNewSpinbutton, 1000);
  }
  
  // Iniciar verificação periódica
  checkForNewSpinbutton();
  
  // Também usar MutationObserver nativo para melhor detecção
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          const $spinbutton = $('[role="spinbutton"]');
          if ($spinbutton.length && !$spinbutton.attr('data-currency-configured')) {
            console.log('🔄 MutationObserver: Novo spinbutton detectado');
            inicializar();
          }
        }
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
  
  // Função global para debug
  window.debugReinoCurrency = function() {
    console.log('🔧 Debug Reino Currency (jQuery):');
    console.log('jQuery disponível:', typeof $ !== 'undefined');
    console.log('Currency.js carregado:', currencyLoaded);
    console.log('Currency.js disponível:', typeof currency !== 'undefined');
    console.log('Currency.js é função:', typeof currency === 'function');
    
    // Testar Currency.js
    if (typeof currency === 'function') {
      try {
        const teste = currency(100);
        console.log('✅ Currency.js funcionando, teste:', teste.format());
      } catch (error) {
        console.error('❌ Currency.js com erro:', error);
      }
    }
    
    const $input = $('[role="spinbutton"]');
    console.log('Spinbutton jQuery:', $input);
    
    if ($input.length) {
      console.log('Configurado:', $input.attr('data-currency-configured'));
      console.log('Valor:', $input.val());
    }
    
    // Testar formatação
    console.log('🧪 Teste formatação global:');
    console.log('formatarMoedaReino("100000"):', window.formatarMoedaReino('100000'));
    
    inicializar();
  };
  
  // Expor função de formatação globalmente
  window.formatarMoedaReino = function(valor) {
    if (currencyLoaded && typeof currency === 'function') {
      try {
        const currencyConfig = {
          symbol: 'R$ ',
          precision: 2,
          separator: ',',
          delimiter: '.',
          format: '%s%v'
        };
        
        const numeros = String(valor).replace(/[^\d]/g, '');
        if (!numeros) return '';
        
        const centavos = parseInt(numeros, 10);
        const reais = centavos / 100;
        
        return currency(reais, currencyConfig).format();
      } catch (error) {
        console.error('❌ Erro Currency.js na função global:', error);
        // Fallback para formatação nativa
      }
    }
    
    // Formatação nativa (fallback)
    const numeros = String(valor).replace(/[^\d]/g, '');
    if (!numeros) return '';
    
    const centavos = parseInt(numeros, 10);
    const reais = centavos / 100;
    
    return reais.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    });
  };
  
  // Função para aplicar formatação a qualquer input manualmente
  window.aplicarCurrencyInput = function(selector) {
    const $target = $(selector);
    
    if (!$target.length) {
      console.log('❌ Elemento não encontrado:', selector);
      return false;
    }
    
    $target.attr('data-currency-configured', 'false');
    $target.attr('id', 'currency');
    
    inicializar();
    return true;
  };
  
})(jQuery);
</script>

<!-- Verificar se jQuery está disponível -->
<script>
if (typeof jQuery === 'undefined') {
  console.log('⚠️ jQuery não encontrado, carregando...');
  
  // Carregar jQuery se não estiver disponível
  const jqueryScript = document.createElement('script');
  jqueryScript.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
  jqueryScript.onload = function() {
    console.log('✅ jQuery carregado');
  };
  document.head.appendChild(jqueryScript);
} else {
  console.log('✅ jQuery já disponível');
}
</script>
