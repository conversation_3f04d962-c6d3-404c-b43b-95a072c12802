import { BaseComponent } from '$core/base-component';
import { eventBus } from '$core/event-bus';
import type { ComponentConfig } from '$types/component';

/**
 * Currency Input Component
 * Automatically formats inputs with data-currency attribute
 * Optimized for Webflow compatibility
 *
 * Usage:
 * <input type="text" data-currency data-symbol="R$" data-precision="2" />
 */
export class CurrencyInput extends BaseComponent {
  static componentName = 'currency-input';

  private input: HTMLInputElement;
  private symbol: string;
  private precision: number;
  private separator: string;
  private decimal: string;
  private rawValue: number = 0;

  constructor(config: ComponentConfig) {
    super(config);

    // The element itself should be the input
    this.input = this.element as HTMLInputElement;
    if (this.input.tagName !== 'INPUT') {
      throw new Error('Currency input component must be applied directly to an input element');
    }

    // Currency configuration from data attributes
    this.symbol = this.input.dataset.symbol || 'R$';
    this.precision = parseInt(this.input.dataset.precision || '2');
    this.separator = this.input.dataset.separator || '.';
    this.decimal = this.input.dataset.decimal || ',';
  }

  public init(): void {
    this.setupInput();
    this.bindEvents();
    this.formatInitialValue();
    this.setInitialized();
  }

  private setupInput(): void {
    // Set input attributes for better UX
    this.input.setAttribute('inputmode', 'decimal');
    this.input.setAttribute('autocomplete', 'off');
    this.input.setAttribute('placeholder', `${this.symbol} 0${this.decimal}00`);

    // Add currency classes
    this.input.classList.add('currency-input');
    this.element.classList.add('currency-input-wrapper');
  }

  private bindEvents(): void {
    // Real-time formatting on input
    this.addEventListener(this.input, 'input', this.handleInput.bind(this));

    // Final formatting on blur
    this.addEventListener(this.input, 'blur', this.handleBlur.bind(this));

    // Handle focus for better editing
    this.addEventListener(this.input, 'focus', this.handleFocus.bind(this));
  }

  private handleInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const cursorPosition = target.selectionStart || 0;
    const oldValue = target.value;
    const oldLength = oldValue.length;

    // Format the value
    const formattedValue = this.formatCurrency(oldValue);
    target.value = formattedValue;

    // Adjust cursor position
    const newLength = formattedValue.length;
    const lengthDiff = newLength - oldLength;
    let newCursorPos = cursorPosition + lengthDiff;

    // Keep cursor after currency symbol
    const symbolLength = this.symbol.length + 1; // +1 for space
    if (newCursorPos < symbolLength) newCursorPos = symbolLength;
    if (newCursorPos > newLength) newCursorPos = newLength;

    // Set cursor position
    setTimeout(() => {
      target.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);

    // Update raw value and emit event
    this.updateRawValue(formattedValue);
  }

  private handleBlur(): void {
    // Ensure proper formatting on blur
    const formattedValue = this.formatCurrency(this.input.value);
    this.input.value = formattedValue;
    this.updateRawValue(formattedValue);
  }

  private handleFocus(): void {
    // Keep formatted value on focus (better for Webflow)
    // No special handling needed as we maintain formatted value
  }

  private formatCurrency(value: string): string {
    // Remove all non-numeric characters
    const numericValue = String(value).replace(/[^\d]/g, '');

    if (!numericValue) return '';

    // Convert to decimal value (considering cents)
    const cents = parseInt(numericValue, 10);
    const reais = cents / Math.pow(10, this.precision);

    // Format using Brazilian locale
    const formatted = reais.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: this.precision,
      maximumFractionDigits: this.precision,
    });

    // Replace R$ with custom symbol if needed
    if (this.symbol !== 'R$') {
      return formatted.replace('R$', this.symbol);
    }

    return formatted;
  }

  private updateRawValue(formattedValue: string): void {
    // Extract numeric value
    const numericString = formattedValue.replace(/[^\d]/g, '');
    const cents = parseInt(numericString, 10) || 0;
    this.rawValue = cents / Math.pow(10, this.precision);

    // Emit change event
    eventBus.emit('currency:change', {
      element: this.element,
      value: this.rawValue,
      formatted: formattedValue,
    });
  }

  private formatInitialValue(): void {
    const initialValue = this.input.value || this.input.dataset.value || '';
    if (initialValue) {
      const formattedValue = this.formatCurrency(initialValue);
      this.input.value = formattedValue;
      this.updateRawValue(formattedValue);
    }
  }

  /**
   * Get the raw numeric value
   */
  public getValue(): number {
    return this.rawValue;
  }

  /**
   * Set the currency value
   */
  public setValue(value: number): void {
    this.rawValue = value;
    const formattedValue = this.formatCurrency(value.toString());
    this.input.value = formattedValue;

    eventBus.emit('currency:change', {
      element: this.element,
      value: this.rawValue,
      formatted: formattedValue,
    });
  }

  /**
   * Get the formatted currency string
   */
  public getFormattedValue(): string {
    return this.formatCurrency(this.rawValue.toString());
  }

  /**
   * Reset the input
   */
  public reset(): void {
    this.setValue(0);
  }

  /**
   * Validate the current value
   */
  public validate(): boolean {
    const min = parseFloat(this.input.dataset.min || '0');
    const max = parseFloat(this.input.dataset.max || 'Infinity');

    return this.rawValue >= min && this.rawValue <= max;
  }

  public destroy(): void {
    // BaseComponent will handle event listener cleanup
    // Remove classes
    this.input.classList.remove('currency-input');
    this.element.classList.remove('currency-input-wrapper');

    super.destroy();
  }
}
