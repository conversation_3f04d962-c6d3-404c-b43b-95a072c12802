// Script para implementar Currency.js no input com id "currency"

// Função para injetar e implementar Currency.js
function implementCurrencyJS() {
  // 1. Procurar o input baseado na estrutura HTML vista no Playwright
  console.log("🔍 Procurando input baseado na estrutura do Playwright...");

  let currencyInput = document.getElementById("currency");

  if (!currencyInput) {
    console.log('❌ Input com id "currency" não encontrado');
    console.log("📝 Procurando usando estrutura HTML específica...");

    // Método 1: Procurar spinbutton (baseado no Playwright snapshot)
    const spinbutton = document.querySelector('[role="spinbutton"]');
    if (spinbutton) {
      console.log("🎯 Spinbutton encontrado (ref=e45):", spinbutton);
      spinbutton.id = "currency";
      currencyInput = spinbutton;
      console.log('✅ ID "currency" adicionado ao spinbutton');
    }

    // Método 2: Procurar dentro do form "Email Form" (ref=e25)
    if (!currencyInput) {
      const emailForm =
        document.querySelector('form[aria-label="Email Form"]') ||
        document.querySelector('form[name="email-form"]') ||
        document.querySelector("form");

      if (emailForm) {
        console.log("📝 Form 'Email Form' encontrado:", emailForm);
        const formInput =
          emailForm.querySelector('input[type="number"]') ||
          emailForm.querySelector("input");
        e;
        if (formInput) {
          formInput.id = "currency";
          currencyInput = formInput;
          console.log("✅ Input do form configurado como currency");
        }
      }
    }

    // Método 3: Procurar por contexto "patrimônio atual"
    if (!currencyInput) {
      const patrimonioHeading = Array.from(
        document.querySelectorAll("h2, h3, div")
      ).find(
        (el) => el.textContent && el.textContent.includes("patrimônio atual")
      );

      if (patrimonioHeading) {
        console.log(
          "📍 Seção 'patrimônio atual' encontrada:",
          patrimonioHeading
        );
        const contextInput =
          patrimonioHeading.closest("div").querySelector("input") ||
          patrimonioHeading.parentElement.querySelector("input");
        if (contextInput) {
          contextInput.id = "currency";
          currencyInput = contextInput;
          console.log("✅ Input contextual configurado como currency");
        }
      }
    }

    if (!currencyInput) {
      console.log("❌ Nenhum input adequado encontrado na estrutura HTML");
      return;
    }
  }

  // 2. Injetar Currency.js se não estiver carregado
  if (typeof currency === "undefined") {
    console.log("📦 Carregando Currency.js...");

    const script = document.createElement("script");
    script.src =
      "https://cdn.jsdelivr.net/npm/currency.js@2.0.4/dist/currency.min.js";
    script.onload = function () {
      console.log("✅ Currency.js carregado com sucesso");
      // Aguardar um pouco para garantir que a biblioteca esteja disponível
      setTimeout(() => {
        setupCurrencyFormatting();
      }, 100);
    };
    script.onerror = function () {
      console.error("❌ Erro ao carregar Currency.js");
      // Implementar formatação manual como fallback
      setupManualCurrencyFormatting();
    };
    document.head.appendChild(script);
  } else {
    console.log("✅ Currency.js já carregado");
    setupCurrencyFormatting();
  }

  // 3. Configurar formatação de moeda
  function setupCurrencyFormatting() {
    const input = document.getElementById("currency");
    if (!input) return;

    console.log("🎨 Configurando formatação de moeda...");

    // Verificar se Currency.js está disponível
    if (typeof currency === "undefined") {
      console.log("⚠️ Currency.js não disponível, usando formatação manual");
      setupManualCurrencyFormatting();
      return;
    }

    try {
      // Configuração para Real Brasileiro
      const currencyConfig = {
        symbol: "R$ ",
        precision: 2,
        separator: ",",
        delimiter: ".",
        format: "%s%v",
      };

      // Função para formatar valor usando Currency.js
      function formatCurrency(value) {
        try {
          return currency(value, currencyConfig).format();
        } catch (error) {
          console.error("Erro na formatação:", error);
          return formatManualCurrency(value);
        }
      }

      // Aplicar formatação inicial se houver valor
      if (input.value) {
        input.value = formatCurrency(input.value);
      }

      // Event listeners para formatação em tempo real
      input.addEventListener("input", function (e) {
        const cursorPosition = e.target.selectionStart;
        const oldValue = e.target.value;
        const oldLength = oldValue.length;

        // Remove formatação para obter apenas números
        const numericValue = oldValue.replace(/[^0-9]/g, "");

        if (numericValue) {
          // Converte para decimal (divide por 100 para considerar centavos)
          const decimalValue = parseFloat(numericValue) / 100;
          const formattedValue = formatCurrency(decimalValue);

          e.target.value = formattedValue;

          // Ajusta posição do cursor
          const newLength = formattedValue.length;
          const newPosition = cursorPosition + (newLength - oldLength);
          e.target.setSelectionRange(newPosition, newPosition);
        }
      });

      // Formatação quando o campo perde o foco
      input.addEventListener("blur", function (e) {
        if (e.target.value && !e.target.value.includes("R$")) {
          e.target.value = formatCurrency(e.target.value);
        }
      });

      // Adicionar atributos para melhor UX
      input.setAttribute("placeholder", "R$ 0,00");
      input.setAttribute("data-currency", "true");

      console.log("✅ Formatação de moeda configurada com sucesso!");

      // Teste da formatação
      console.log("🧪 Testando formatação:");
      console.log("1000 -> ", formatCurrency(1000));
      console.log("1234.56 -> ", formatCurrency(1234.56));
      console.log("100000 -> ", formatCurrency(100000));
    } catch (error) {
      console.error("❌ Erro ao configurar Currency.js:", error);
      setupManualCurrencyFormatting();
    }
  }

  // Função de formatação manual como fallback
  function setupManualCurrencyFormatting() {
    const input = document.getElementById("currency");
    if (!input) return;

    console.log("🔧 Configurando formatação manual...");

    // Função para formatar valor manualmente (otimizada para Webflow)
    function formatManualCurrency(value) {
      // Remove tudo que não é número
      const numbers = String(value).replace(/[^\d]/g, "");

      if (!numbers) return "";

      // Converte para número (considerando centavos)
      const cents = parseInt(numbers, 10);
      const reais = cents / 100;

      // Formata para moeda brasileira
      return reais.toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    }

    // Aplicar formatação inicial se houver valor
    if (input.value) {
      input.value = formatManualCurrency(input.value);
    }

    // Event listeners para formatação em tempo real
    input.addEventListener("input", function (e) {
      const inputElement = e.target;
      const cursorPosition = inputElement.selectionStart;
      const oldValue = inputElement.value;
      const oldLength = oldValue.length;

      // Formatar valor
      const formattedValue = formatManualCurrency(oldValue);
      inputElement.value = formattedValue;

      // Calcular nova posição do cursor
      const newLength = formattedValue.length;
      const lengthDiff = newLength - oldLength;
      let newCursorPos = cursorPosition + lengthDiff;

      // Ajustar cursor para não ficar no meio do símbolo da moeda
      if (newCursorPos < 3) newCursorPos = 3; // Depois de "R$ "
      if (newCursorPos > newLength) newCursorPos = newLength;

      // Aplicar nova posição do cursor
      setTimeout(() => {
        inputElement.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
    });

    // Formatação quando o campo perde o foco
    input.addEventListener("blur", function (e) {
      if (e.target.value && !e.target.value.includes("R$")) {
        e.target.value = formatManualCurrency(e.target.value);
      }
    });

    // Adicionar atributos para melhor UX
    input.setAttribute("placeholder", "R$ 0,00");
    input.setAttribute("data-currency", "true");

    console.log("✅ Formatação manual configurada com sucesso!");

    // Teste da formatação
    console.log("🧪 Testando formatação manual:");
    console.log("1000 -> ", formatManualCurrency(1000));
    console.log("1234.56 -> ", formatManualCurrency(1234.56));
    console.log("100000 -> ", formatManualCurrency(100000));
  }

  // Função auxiliar para formatação manual (disponível globalmente)
  function formatManualCurrency(value) {
    // Remove tudo que não é número
    const numbers = String(value).replace(/[^\d]/g, "");

    if (!numbers) return "";

    // Converte para número (considerando centavos)
    const cents = parseInt(numbers, 10);
    const reais = cents / 100;

    // Formata para moeda brasileira
    return reais.toLocaleString("pt-BR", {
      style: "currency",
      currency: "BRL",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }
}

// Executar quando a página estiver carregada
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", implementCurrencyJS);
} else {
  implementCurrencyJS();
}

// Também executar imediatamente para páginas já carregadas
implementCurrencyJS();

// Observer para mudanças no DOM (específico para Webflow)
if (typeof MutationObserver !== "undefined") {
  const observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
      if (mutation.type === "childList" || mutation.type === "attributes") {
        // Verificar se apareceu um spinbutton sem formatação
        const spinbutton = document.querySelector('[role="spinbutton"]');
        if (spinbutton && !spinbutton.hasAttribute("data-currency")) {
          console.log(
            "🔄 Novo spinbutton detectado, reaplicando formatação..."
          );
          setTimeout(implementCurrencyJS, 100);
        }
      }
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ["role", "class", "id"],
  });
}

// Executar periodicamente como fallback (para garantir que funcione)
setInterval(function () {
  const spinbutton = document.querySelector('[role="spinbutton"]');
  if (spinbutton && !spinbutton.hasAttribute("data-currency")) {
    console.log("🔄 Verificação periódica: reaplicando formatação...");
    implementCurrencyJS();
  }
}, 2000);

// Função de debug para testar manualmente
window.debugCurrency = function () {
  console.log("🔧 Debug Currency - Estado atual:");

  const currencyInput = document.getElementById("currency");
  console.log("Input currency:", currencyInput);

  const spinbutton = document.querySelector('[role="spinbutton"]');
  console.log("Spinbutton:", spinbutton);

  const form = document.querySelector("form");
  console.log("Form:", form);

  if (currencyInput) {
    console.log("Valor atual:", currencyInput.value);
    console.log(
      "Possui data-currency:",
      currencyInput.hasAttribute("data-currency")
    );
  }

  // Tentar aplicar formatação manualmente
  implementCurrencyJS();
};
