// Script otimizado para Webflow - Currency.js Implementation
// Para usar no Webflow, adicione este script antes do </body>

(function () {
  "use strict";

  console.log("🚀 Iniciando Currency.js para Webflow...");

  // Função principal de implementação
  function initWebflowCurrency() {
    // Procurar input com id "currency"
    let currencyInput = document.getElementById("currency");

    if (!currencyInput) {
      console.log('❌ Input com id "currency" não encontrado');

      // Procurar spinbutton e adicionar id
      const spinbutton = document.querySelector('[role="spinbutton"]');
      if (spinbutton) {
        spinbutton.id = "currency";
        currencyInput = spinbutton;
        console.log('✅ ID "currency" adicionado ao spinbutton');
      } else {
        console.log("❌ Nenhum spinbutton encontrado");
        return;
      }
    }

    console.log("✅ Input encontrado:", currencyInput);

    // Formatação manual (mais confiável para Webflow)
    function formatBRLCurrency(value) {
      // Remove tudo que não é número
      const numericValue = String(value).replace(/[^0-9]/g, "");

      if (!numericValue) return "R$ 0,00";

      // Converte para número decimal
      const decimalValue = parseFloat(numericValue) / 100;

      // Formata para moeda brasileira
      return (
        "R$ " +
        decimalValue.toLocaleString("pt-BR", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })
      );
    }

    // Função para obter valor numérico
    function getNumericValue(formattedValue) {
      const cleaned = String(formattedValue).replace(/[^0-9]/g, "");
      return parseFloat(cleaned) / 100;
    }

    // Aplicar formatação inicial
    if (currencyInput.value) {
      currencyInput.value = formatBRLCurrency(currencyInput.value);
    }

    // Event listener para formatação em tempo real
    currencyInput.addEventListener("input", function (e) {
      const input = e.target;
      const cursorPosition = input.selectionStart;
      const oldValue = input.value;
      const oldLength = oldValue.length;

      // Formatar valor
      const formattedValue = formatBRLCurrency(oldValue);
      input.value = formattedValue;

      // Ajustar cursor
      const newLength = formattedValue.length;
      const lengthDifference = newLength - oldLength;
      const newCursorPosition = Math.max(0, cursorPosition + lengthDifference);

      // Definir nova posição do cursor
      setTimeout(() => {
        input.setSelectionRange(newCursorPosition, newCursorPosition);
      }, 0);
    });

    // Formatação ao perder foco
    currencyInput.addEventListener("blur", function (e) {
      if (e.target.value && !e.target.value.includes("R$")) {
        e.target.value = formatBRLCurrency(e.target.value);
      }
    });

    // Melhorar UX
    currencyInput.setAttribute("placeholder", "R$ 0,00");
    currencyInput.setAttribute("data-currency", "true");

    // Adicionar estilos CSS se necessário
    const style = document.createElement("style");
    style.textContent = `
      input[data-currency="true"] {
        text-align: right;
        font-family: monospace;
      }
      
      input[data-currency="true"]:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
      }
    `;
    document.head.appendChild(style);

    console.log("✅ Formatação de moeda configurada com sucesso!");

    // Teste
    console.log("🧪 Testes:");
    console.log("100000 -> ", formatBRLCurrency(100000));
    console.log("1234567 -> ", formatBRLCurrency(1234567));
    console.log("50000 -> ", formatBRLCurrency(50000));

    // Expor funções globalmente para debugging
    window.formatBRLCurrency = formatBRLCurrency;
    window.getNumericValue = getNumericValue;
  }

  // Aguardar Webflow carregar
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initWebflowCurrency);
  } else {
    initWebflowCurrency();
  }

  // Também tentar após um delay para garantir que o Webflow terminou
  setTimeout(initWebflowCurrency, 1000);

  // Observar mudanças no DOM (caso o Webflow faça alterações dinâmicas)
  if (typeof MutationObserver !== "undefined") {
    const observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.type === "childList") {
          const currencyInput = document.getElementById("currency");
          if (currencyInput && !currencyInput.hasAttribute("data-currency")) {
            console.log("🔄 Reaplicando formatação após mudança no DOM");
            initWebflowCurrency();
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }
})();

// Também adicionar um fallback que pode ser chamado manualmente
window.initWebflowCurrency = function () {
  console.log("🔧 Inicialização manual do Currency.js");

  const input = document.getElementById("currency");
  if (!input) {
    console.log('❌ Input com id "currency" não encontrado');
    return false;
  }

  // Aplicar formatação simples
  input.addEventListener("input", function (e) {
    const value = e.target.value.replace(/[^0-9]/g, "");
    if (value) {
      const decimal = parseFloat(value) / 100;
      e.target.value =
        "R$ " +
        decimal.toLocaleString("pt-BR", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
    }
  });

  console.log("✅ Formatação manual aplicada");
  return true;
};
