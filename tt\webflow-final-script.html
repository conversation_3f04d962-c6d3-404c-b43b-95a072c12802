// SCRIPT FINAL PARA WEBFLOW - Reino Capital Calculadora com Currency.js
// Copie este código e cole no final da página antes do </body>

<!-- Carregar Currency.js -->
<script src="https://cdn.jsdelivr.net/npm/currency.js@2.0.4/dist/currency.min.js"></script>

<script>
(function() {
  console.log('🚀 Reino Capital - Currency Formatter com Currency.js iniciado');
  
  function aplicarFormatacao() {
    // Procurar input baseado na estrutura HTML específica do Playwright
    let input = document.querySelector('[role="spinbutton"]') || 
               document.querySelector('form input[type="number"]') ||
               document.querySelector('form input');
    
    if (!input) {
      console.log('❌ Input não encontrado');
      return false;
    }
    
    // Verificar se já foi configurado
    if (input.hasAttribute('data-currency-configured')) {
      return true;
    }
    
    console.log('✅ Input encontrado, aplicando formatação...', input);
    
    // Marcar como configurado
    input.setAttribute('data-currency-configured', 'true');
    input.id = 'currency';
    
    // Verificar se Currency.js está disponível
    if (typeof currency === 'undefined') {
      console.log('⚠️ Currency.js não disponível, usando formatação nativa');
      
      // Fallback com formatação nativa
      function formatarMoeda(valor) {
        const numeros = String(valor).replace(/[^\d]/g, '');
        if (!numeros) return '';
        
        const centavos = parseInt(numeros, 10);
        const reais = centavos / 100;
        
        return reais.toLocaleString('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        });
      }
    } else {
      console.log('✅ Currency.js disponível, usando biblioteca');
      
      // Configuração Currency.js para Real Brasileiro
      const currencyConfig = {
        symbol: 'R$ ',
        precision: 2,
        separator: ',',
        delimiter: '.',
        format: '%s%v'
      };
      
      // Função de formatação com Currency.js
      function formatarMoeda(valor) {
        try {
          const numeros = String(valor).replace(/[^\d]/g, '');
          if (!numeros) return '';
          
          const centavos = parseInt(numeros, 10);
          const reais = centavos / 100;
          
          return currency(reais, currencyConfig).format();
        } catch (error) {
          console.error('Erro Currency.js:', error);
          // Fallback para formatação nativa
          const centavos = parseInt(String(valor).replace(/[^\d]/g, ''), 10);
          const reais = centavos / 100;
          return reais.toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
          });
        }
      }
    }
    
    // Aplicar formatação inicial
    if (input.value) {
      input.value = formatarMoeda(input.value);
    }
    
    // Event listener para formatação em tempo real
    input.addEventListener('input', function(e) {
      const cursor = e.target.selectionStart;
      const valorAntigo = e.target.value;
      const tamanhoAntigo = valorAntigo.length;
      
      const valorFormatado = formatarMoeda(valorAntigo);
      e.target.value = valorFormatado;
      
      // Ajustar cursor
      const novoTamanho = valorFormatado.length;
      const diferenca = novoTamanho - tamanhoAntigo;
      let novoCursor = cursor + diferenca;
      
      if (novoCursor < 3) novoCursor = 3; // Depois de "R$ "
      if (novoCursor > novoTamanho) novoCursor = novoTamanho;
      
      setTimeout(() => {
        e.target.setSelectionRange(novoCursor, novoCursor);
      }, 0);
    });
    
    // Formatação ao perder foco
    input.addEventListener('blur', function(e) {
      if (e.target.value && !e.target.value.includes('R$')) {
        e.target.value = formatarMoeda(e.target.value);
      }
    });
    
    // Melhorar aparência
    input.setAttribute('placeholder', 'R$ 0,00');
    input.style.textAlign = 'right';
    input.style.fontWeight = '500';
    
    // Teste da formatação
    console.log('🧪 Testando formatação:');
    console.log('100000 →', formatarMoeda('100000'));
    console.log('1234567 →', formatarMoeda('1234567'));
    console.log('50000 →', formatarMoeda('50000'));
    
    console.log('✅ Formatação aplicada com sucesso!');
    return true;
  }
  
  // Aguardar Currency.js carregar antes de aplicar formatação
  function aguardarCurrencyJS() {
    if (typeof currency !== 'undefined') {
      console.log('✅ Currency.js carregado');
      aplicarFormatacao();
    } else {
      console.log('⏳ Aguardando Currency.js...');
      setTimeout(aguardarCurrencyJS, 100);
    }
  }
  
  // Tentar aplicar imediatamente
  aplicarFormatacao();
  
  // Tentar após DOM carregar
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', aguardarCurrencyJS);
  } else {
    aguardarCurrencyJS();
  }
  
  // Tentativas com delay (para aguardar Webflow e Currency.js)
  setTimeout(aguardarCurrencyJS, 500);
  setTimeout(aguardarCurrencyJS, 1000);
  setTimeout(aguardarCurrencyJS, 2000);
  
  // Observer para mudanças no DOM
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        const spinbutton = document.querySelector('[role="spinbutton"]');
        if (spinbutton && !spinbutton.hasAttribute('data-currency-configured')) {
          console.log('🔄 Novo spinbutton detectado, aplicando formatação...');
          aguardarCurrencyJS();
        }
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // Função global para debug
  window.debugReinoCurrency = function() {
    console.log('🔧 Debug Reino Currency:');
    console.log('Currency.js disponível:', typeof currency !== 'undefined');
    const input = document.querySelector('[role="spinbutton"]');
    console.log('Spinbutton:', input);
    if (input) {
      console.log('Configurado:', input.hasAttribute('data-currency-configured'));
      console.log('Valor:', input.value);
    }
    aguardarCurrencyJS();
  };
  
  // Expor função de formatação globalmente
  window.formatarMoedaReino = function(valor) {
    if (typeof currency !== 'undefined') {
      const currencyConfig = {
        symbol: 'R$ ',
        precision: 2,
        separator: ',',
        delimiter: '.',
        format: '%s%v'
      };
      
      const numeros = String(valor).replace(/[^\d]/g, '');
      if (!numeros) return '';
      
      const centavos = parseInt(numeros, 10);
      const reais = centavos / 100;
      
      return currency(reais, currencyConfig).format();
    } else {
      const numeros = String(valor).replace(/[^\d]/g, '');
      if (!numeros) return '';
      
      const centavos = parseInt(numeros, 10);
      const reais = centavos / 100;
      
      return reais.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      });
    }
  };
  
})();
</script>
