/**
 * Lightweight Ativos Module Styles
 * 
 * Optimized for performance with minimal animations
 * Removed heavy GSAP animations to improve site speed
 */

/* Base sortable styles - minimal and fast */
.ativos_main-list,
.ativos_main_drop_area {
  position: relative;
  min-height: 50px;
}

.ativos_main_drop_area {
  width: 100%;
  height: auto;
  min-height: 100px;
  display: flex;
  flex-direction: row; /* Items lado a lado */
  flex-wrap: wrap; /* Quebra linha quando não tem espaço */
  gap: 12px; /* Espaço entre os itens */
  padding: 16px;
  align-items: flex-start; /* <PERSON><PERSON><PERSON> itens no topo */
  align-content: flex-start; /* <PERSON><PERSON><PERSON> as linhas no topo */
}

/* Drag states - subtle and clean visual feedback */
.ativos-ghost {
  opacity: 1 !important;
  border: 1px dashed #daa521 !important;
}

.ativos-chosen {
  cursor: grabbing !important;
  z-index: 1000;
  border: 1px solid #daa521 !important;
  border-radius: 4px;
  transition: border-color 0.2s ease;
}

.ativos-drag {
  cursor: grabbing !important;
  z-index: 1001;
}

.ativos-fallback {
  cursor: grabbing !important;
  opacity: 1;
  border: 2px dashed #daa521;
  border-radius: 8px;
}

/* Individual item states - lightweight */
.ativos_main-list .w-dyn-item {
  cursor: grab;
  position: relative;
  user-select: none;
}

.ativos_main-list .w-dyn-item:hover {
  transform: translateY(-1px);
  transition: transform 0.1s ease;
}

.ativos_main_drop_area .w-dyn-item {
  cursor: default;
  position: relative;
  border-radius: 8px;
  user-select: none;
  margin-bottom: 0;
  flex-shrink: 0;
  min-width: auto;
  max-width: none;
}

.ativos_main_drop_area .w-dyn-item:hover {
  opacity: 1;
  transition: opacity 0.1s ease;
}

/* Drag states - minimal and clean for shared groups */
.w-dyn-item.ativos-dragging {
  cursor: grabbing !important;
  border: 1px solid #daa521 !important;
  border-radius: 4px;
  transition: border-color 0.2s ease;
}

/* Drop area states */
.drop_ativos_area-wrapper {
  position: relative;
}

/* Main drop area states */
.ativos_main_drop_area.ativos-empty {
  border-color: rgba(0, 0, 0, 0.1);
}

.ativos_main_drop_area.ativos-has-items {
  border-color: rgba(218, 165, 33, 0.3);
}

/* Active sorting state - subtle visual feedback */
body.ativos-sorting .drop_ativos_area-wrapper,
body.ativos-sorting .ativos_main_drop_area {
  border-color: #daa521 !important;
  background-color: rgba(218, 165, 33, 0.02);
  transition: all 0.2s ease;
}

/* Subtle highlight for drop zones during drag */
body.ativos-sorting .ativos_main_drop_area:hover {
  background-color: rgba(218, 165, 33, 0.05);
}

/* Keep all items fully visible during drag */
body.ativos-sorting .w-dyn-item {
  transition: none; /* Remove opacity transitions for cleaner experience */
}

/* Dynamic state for empty source containers */
.texto-info.ativo {
  /* This class is toggled when source container is empty */
  /* Webflow combo class styling will be applied automatically */
  /* Placeholder property to avoid empty ruleset warning */
  position: relative;
}

/* Styling for new custom assets - now using #pillAtivo template */
.new-asset-item {
  /* Minimal styling since we're using existing Webflow element structure */
  position: relative;
}

/* Optional: Add subtle indicator for custom assets if needed */

/* Counter styles - simple and fast */
.counter_ativos {
  font-weight: 600;
  color: #daa521;
}

/* Empty state */
.ativos_main-list:empty::after,
.ativos_main_drop_area:empty::after {
  content: 'Arraste seus ativos aqui';
  display: block;
  text-align: center;
  padding: 32px 16px;
  color: rgba(0, 0, 0, 0.4);
  font-style: italic;
  border: 2px dashed rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.ativos_main-list.ativos-has-items:empty::after,
.ativos_main_drop_area.ativos-has-items:empty::after {
  display: none;
}

/* Drag handle - lightweight */
.ativos-handle {
  cursor: grab;
  opacity: 1;
}

.ativos-handle:hover {
  opacity: 1;
}

.ativos-handle:active {
  cursor: grabbing;
}

.ativos_main-list .w-dyn-item:hover .icon-draggable,
.w-dyn-item.ativos-dragging .icon-draggable {
  visibility: visible;
  opacity: 1;
}

.ativos_main_drop_area .icon-draggable {
  display: none !important;
}

/* Header elements */
.drop_header_are-wrapper,
.ativos_counter-wrapper,
.ativos_clean-button {
  pointer-events: auto;
  cursor: default;
  user-select: auto;
}

/* Clean button */
.ativos_clean-button {
  cursor: pointer;
}

/* Mobile responsive - ajustado para o layout flex */
@media (max-width: 767px) {
  .ativos_main_drop_area {
    gap: 8px; /* Gap menor no mobile */
    padding: 12px; /* Padding menor */
  }

  .drop_ativos_area-wrapper {
    padding-left: 16px;
    padding-right: 16px;
    min-height: 400px;
  }

  /* Minimal effects on mobile for better performance */
  .ativos-chosen {
    transform: scale(1.01);
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .drop_ativos_area-wrapper {
    padding-left: 24px;
    padding-right: 24px;
    min-height: 450px;
  }
}

@media (min-width: 992px) {
  .drop_ativos_area-wrapper {
    min-height: 475px;
  }

  .w-dyn-item:hover {
    transform: translateY(-2px);
    transition: transform 0.1s ease;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ativos-ghost {
    border-color: #000 !important;
  }

  .ativos-chosen {
    border-color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .ativos_main-list,
  .w-dyn-item,
  .drop_ativos_area-wrapper,
  .counter_ativos,
  .icon-draggable,
  .ativos-handle {
    transition: none !important;
    animation: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .w-dyn-item {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .ativos_main-list:empty::after,
  .ativos_main_drop_area:empty::after {
    color: rgba(255, 255, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.1);
  }
}
