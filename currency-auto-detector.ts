import { CurrencyInput } from '$components/forms/currency-input';
import { componentRegistry } from '$core/component-registry';

/**
 * Auto-detects and initializes currency inputs
 * This module automatically scans for inputs with data-currency attribute
 * Enhanced with Webflow-specific detection logic
 */
export class CurrencyAutoDetector {
  private static instance: CurrencyAutoDetector;
  private observer: MutationObserver | null = null;
  private initialized = false;
  private periodicCheckInterval: number | null = null;

  public static getInstance(): CurrencyAutoDetector {
    if (!CurrencyAutoDetector.instance) {
      CurrencyAutoDetector.instance = new CurrencyAutoDetector();
    }
    return CurrencyAutoDetector.instance;
  }

  /**
   * Initialize the auto-detector
   */
  public init(): void {
    if (this.initialized) return;

    // Register the currency input component
    componentRegistry.register(CurrencyInput);

    // Convert existing inputs with data-currency attribute
    this.convertExistingInputs();

    // Start observing for new inputs
    this.startObserving();

    // Start periodic checking for Webflow compatibility
    this.startPeriodicChecking();

    this.initialized = true;
    // Currency auto-detector initialized with Webflow support
  }

  /**
   * Convert existing inputs with data-currency attribute
   * Enhanced with Webflow-specific detection
   */
  private convertExistingInputs(): void {
    // Standard data-currency attribute detection
    const currencyInputs = document.querySelectorAll<HTMLInputElement>('input[data-currency]');

    currencyInputs.forEach((input) => {
      this.convertToCurrencyInput(input);
    });

    // Webflow-specific detection for spinbuttons
    const spinbuttons = document.querySelectorAll<HTMLInputElement>('input[role="spinbutton"]');
    spinbuttons.forEach((input) => {
      if (this.shouldBeCurrencyInput(input)) {
        this.convertToCurrencyInput(input);
      }
    });
  }

  /**
   * Check if an input should be converted to currency based on context
   */
  private shouldBeCurrencyInput(input: HTMLInputElement): boolean {
    // Check for currency-related keywords in various attributes
    const attributes = [
      input.name,
      input.id,
      input.className,
      input.placeholder,
      input.getAttribute('aria-label'),
      input.getAttribute('data-label'),
    ];

    const currencyKeywords = [
      'price',
      'valor',
      'preco',
      'currency',
      'money',
      'real',
      'reais',
      'cost',
      'custo',
      'amount',
      'quantia',
      'payment',
      'pagamento',
      'investment',
      'investimento',
      'capital',
      'fund',
      'fundo',
    ];

    return attributes.some(
      (attr) =>
        attr &&
        currencyKeywords.some((keyword) => attr.toLowerCase().includes(keyword.toLowerCase()))
    );
  }

  /**
   * Start periodic checking for dynamically added inputs (Webflow compatibility)
   */
  private startPeriodicChecking(): void {
    if (this.periodicCheckInterval) return;

    this.periodicCheckInterval = window.setInterval(() => {
      this.convertExistingInputs();
    }, 2000); // Check every 2 seconds for new inputs
  }

  /**
   * Convert a regular input to a currency input
   */
  private convertToCurrencyInput(input: HTMLInputElement): void {
    // Skip if already converted
    if (input.dataset.component === 'currency-input') return;

    // Add the component attribute
    input.dataset.component = 'currency-input';

    // Set default attributes if not present
    if (!input.dataset.symbol) input.dataset.symbol = 'R$';
    if (!input.dataset.precision) input.dataset.precision = '2';
    if (!input.dataset.separator) input.dataset.separator = ',';
    if (!input.dataset.decimal) input.dataset.decimal = '.';
    if (!input.dataset.format) input.dataset.format = '%s %v';

    // Force input type to text for better formatting control
    input.type = 'text';

    // Input converted to currency
  }

  /**
   * Start observing DOM changes for new currency inputs
   */
  private startObserving(): void {
    if (this.observer) return;

    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as HTMLElement;

            // Check if the added element is a currency input
            if (element.tagName === 'INPUT' && element.hasAttribute('data-currency')) {
              this.convertToCurrencyInput(element as HTMLInputElement);
            }

            // Check for currency inputs within the added element
            const currencyInputs =
              element.querySelectorAll<HTMLInputElement>('input[data-currency]');
            currencyInputs.forEach((input) => {
              this.convertToCurrencyInput(input);
            });
          }
        });
      });
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  /**
   * Stop observing DOM changes and clean up resources
   */
  public destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    if (this.periodicCheckInterval) {
      clearInterval(this.periodicCheckInterval);
      this.periodicCheckInterval = null;
    }

    this.initialized = false;
  }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    CurrencyAutoDetector.getInstance().init();
  });
} else {
  CurrencyAutoDetector.getInstance().init();
}

export const currencyAutoDetector = CurrencyAutoDetector.getInstance();
